# FastAPI and related
fastapi>=0.115.12
pydantic>=2.11.4

# Uvicorn and standard extras
uvicorn>=0.34.2
python-dotenv==1.0.0
websockets==11.0.3
httptools         # For Uvicorn performance, let pip choose compatible for 0.23.2
uvloop ; sys_platform != "win32" # For Uvicorn performance on Linux/macOS

# Core launch_camoufox dependencies
playwright
camoufox[geoip]

# HTTP client and other utilities
aiohttp~=3.11.18  # CRITICAL: Using the newer version from the root project.
                 # The helper's middleware.py usage of aiohttp is likely compatible.
requests==2.31.0
pyjwt==2.8.0

# Stream Proxy
aiosocks~=0.2.6
python-socks~=2.7.1
