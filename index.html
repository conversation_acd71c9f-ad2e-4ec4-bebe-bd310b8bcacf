<!DOCTYPE html>
<html lang="zh-CN" id="html-root">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Studio Proxy Chat</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="webui.css">
</head>

<body>
    <div class="workspace-container">
        <!-- Chat Panel on the Left -->
        <div class="chat-panel">
            <h1>
                <span class="logo">
                    <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </span>
                AI Studio Proxy Chat
            </h1>

            <!-- Navigation -->
            <div class="main-nav">
                <button id="nav-chat" class="nav-button active">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-icon">
                        <path
                            d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    聊天
                </button>
                <button id="nav-model-settings" class="nav-button">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-icon">
                        <path
                            d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    设置
                </button>
                <button id="nav-server-info" class="nav-button">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                        class="nav-icon">
                        <path
                            d="M5 12H19M5 12C3.89543 12 3 11.1046 3 10V6C3 4.89543 3.89543 4 5 4H19C20.1046 4 21 4.89543 21 6V10C21 11.1046 20.1046 12 19 12M5 12C3.89543 12 3 12.8954 3 14V18C3 19.1046 3.89543 20 5 20H19C20.1046 20 21 19.1046 21 18V14C21 12.8954 20.1046 12 19 12M7 8H7.01M7 16H7.01"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    其他
                </button>
                <button id="themeToggleButton" title="切换主题">
                    <svg id="darkModeIcon" class="theme-icon" viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 3a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0V4a1 1 0 0 1 1-1Z" fill="currentColor" />
                        <path d="M12 19a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0v-1a1 1 0 0 1 1-1Z" fill="currentColor" />
                        <path d="M20 12a1 1 0 0 1 1 1 1 1 0 1 1-2 0 1 1 0 0 1 1-1Z" fill="currentColor" />
                        <path d="M4 12a1 1 0 0 1 1 1 1 1 0 1 1-2 0 1 1 0 0 1 1-1Z" fill="currentColor" />
                        <path d="M17.7 6.3a1 1 0 0 1 1.4 0 1 1 0 0 1 0 1.4l-.7.7a1 1 0 0 1-1.4-1.4l.7-.7Z"
                            fill="currentColor" />
                        <path d="M6.3 17.7a1 1 0 0 1 1.4 0 1 1 0 0 1 0 1.4l-.7.7a1 1 0 0 1-1.4-1.4l.7-.7Z"
                            fill="currentColor" />
                        <path d="M17.7 17.7a1 1 0 0 1 0 1.4l-.7.7a1 1 0 0 1-1.4-1.4l.7-.7a1 1 0 0 1 1.4 0Z"
                            fill="currentColor" />
                        <path d="M6.3 6.3a1 1 0 0 1 0 1.4l-.7.7A1 1 0 0 1 4.2 7l.7-.7a1 1 0 0 1 1.4 0Z"
                            fill="currentColor" />
                        <path d="M12 7a5 5 0 1 0 0 10 5 5 0 0 0 0-10Z" fill="currentColor" />
                    </svg>
                    <svg id="lightModeIcon" class="theme-icon" viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M12 18.5c3.59 0 6.5-2.91 6.5-6.5S15.59 5.5 12 5.5 5.5 8.41 5.5 12s2.91 6.5 6.5 6.5Z"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" fill="currentColor" />
                    </svg>
                    
                </button>
            </div>

            <!-- View Container -->
            <div class="view-container">
                <!-- Chat View -->
                <div id="chat-view">
                    <div id="chatbox">
                        <!-- Chat messages will be appended here -->
                    </div>
                    <div id="input-area">
                        <div class="model-selector-container">
                            
                            <select id="modelSelector"></select>
                            <button id="refreshModelsButton" title="刷新模型列表">刷新</button>
                        </div>
                        <textarea id="userInput" placeholder="输入消息... (Shift+Enter 换行)" rows="1"></textarea>
                        <button id="clearButton" class="action-button">清空</button>
                        <button id="sendButton" class="action-button">发送</button>
                    </div>
                </div>

                <!-- Server Info View (hidden by default) -->
                <div id="server-info-view">
                    <div class="server-info-header">
                        <h3>服务器状态与 API 信息</h3>
                        <button id="refreshServerInfoButton" class="action-button" title="刷新状态">刷新</button>
                    </div>

                    <div id="api-info-area" class="info-card">
                        <h3>API 调用信息</h3>
                        <div id="api-info-content">
                            <div class="loading-indicator">
                                <div class="loading-spinner"></div>
                                <span>正在加载 API 信息...</span>
                            </div>
                        </div>
                    </div>

                    <div id="health-status-area" class="info-card">
                        <h3>服务健康检查状态</h3>
                        <div id="health-status-display">
                            <div class="loading-indicator">
                                <div class="loading-spinner"></div>
                                <span>正在加载健康状态...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Model Settings View (hidden by default) -->
                <div id="model-settings-view">
                    <div class="server-info-header">
                        <h3>模型对话设置</h3>
                        <button id="resetModelSettingsButton" class="action-button" title="重置为默认设置">重置</button>
                    </div>

                    <div class="info-card">
                        <h3>系统提示词</h3>
                        <div class="settings-group">
                            <label for="systemPrompt">系统提示词 (System Prompt):</label>
                            <textarea id="systemPrompt" class="settings-textarea" rows="4"
                                placeholder="输入系统提示词..."></textarea>
                            <div class="settings-description">
                                系统提示词会在每次对话开始时发送给模型，用于设置模型的行为和角色。
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>生成参数</h3>
                        <div class="settings-group">
                            <label for="temperatureValue">温度 (Temperature):</label>
                            <div class="settings-slider-container">
                                <input type="range" id="temperatureSlider" class="settings-slider" min="0" max="2" step="0.01" value="1">
                                <input type="number" id="temperatureValue" class="settings-number" min="0" max="2" step="0.01" value="1">
                            </div>
                            <div class="settings-description">
                                控制生成文本的随机性。值越高，回复越随机；值越低，回复越确定。
                            </div>
                        </div>

                        <div class="settings-group">
                            <label for="maxOutputTokensValue">最大输出令牌 (Max Output Tokens):</label>
                            <div class="settings-slider-container">
                                <input type="range" id="maxOutputTokensSlider" class="settings-slider" min="1" max="8192" step="1" value="2048">
                                <input type="number" id="maxOutputTokensValue" class="settings-number" min="1" max="8192" step="1" value="2048">
                            </div>
                            <div class="settings-description">
                                限制模型生成的最大令牌数量。
                            </div>
                        </div>

                        <div class="settings-group">
                            <label for="topPValue">Top P:</label>
                            <div class="settings-slider-container">
                                <input type="range" id="topPSlider" class="settings-slider" min="0" max="1" step="0.01" value="0.95">
                                <input type="number" id="topPValue" class="settings-number" min="0" max="1" step="0.01" value="0.95">
                            </div>
                            <div class="settings-description">
                                控制文本生成的多样性。值越低，生成的文本越集中于高概率词汇。
                            </div>
                        </div>

                        <div class="settings-group">
                            <label for="stopSequences">停止序列 (Stop Sequences):</label>
                            <input type="text" id="stopSequences" class="settings-input" placeholder="用逗号分隔多个停止序列">
                            <div class="settings-description">
                                模型遇到这些序列时会停止生成。多个序列用逗号分隔。
                                <br>留空表示使用服务器默认值。
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>设置保存状态</h3>
                        <div id="settings-status" class="settings-status">
                            参数设置将自动应用于聊天，并保存在本地浏览器中。
                        </div>
                        <button id="saveModelSettingsButton" class="action-button full-width-button">保存设置</button>
                    </div>
                </div>
            </div>

        </div>

        <!-- Sidebar Panel on the Right (Now only for Logs) -->
        <div class="sidebar-panel collapsed" id="sidebarPanel">
            <div id="log-area">
                <div id="log-area-header">
                    <span>系统终端输出日志</span>
                    <button id="clearLogButton" class="action-button icon-button" title="清空日志">清理</button>
                </div>
                <div id="log-terminal-wrapper">
                    <div id="log-terminal">
                        <!-- Log entries will be appended here -->
                    </div>
                </div>
                <div id="log-status" class="log-status">[Log Status] 等待连接...</div>
            </div>
        </div>

        <button id="toggleSidebarButton" title="展开侧边栏">></button>

    </div>

    <script src="webui.js" defer></script>
</body>

</html>